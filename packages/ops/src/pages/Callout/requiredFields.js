import { useState, useEffect } from 'react';
import { get } from 'lodash';
import {
  useGetRecordId,
  useGetOne,
  useUpdate,
  useNotify,
  useRedirect,
  Title,
} from 'react-admin';
import {
  Button,
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Grid,
  Divider,
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import BasicBreadcrumbs from '../../sharedComponents/basicBreadCrumbs';
import { useNavigate } from 'react-router-dom';

const RequiredFieldsEdit = () => {
  const [update] = useUpdate();
  const notify = useNotify();
  const redirect = useRedirect();
  const recordId = useGetRecordId();
  const { data: callout, isLoading } = useGetOne('callouts', {
    id: recordId,
  });

  // Initialize required fields state
  const [requiredFields, setRequiredFields] = useState({
    cover: {
      coverPic: false,
      title: false,
      producer: false,
      director: false,
      writer: false,
    },
    basicInfo: {
      logLine: true,
      format: true,
      tags: true,
      genre: true,
      setting: true,
      runningTime: true,
      status: true,
      projectStatus: true,
    },
    projectDisc: {
      synopsis: true,
      creativeVision: true,
      treatment: true,
      script: true,
    },
    creativeTeam: false,
    castMembers: false,
    artWork: false,
    videos: false,
    financePlan: false,
    budget: false,
    otherDocs: false,
    salesEstimateFile: false,
  });

  const [isSaving, setIsSaving] = useState(false);

  // Update required fields when callout data is loaded
  useEffect(() => {
    if (callout && callout.requiredFields) {
      // Merge with default structure to ensure all fields exist
      setRequiredFields(() => {
        const existingFields = callout.requiredFields;

        return {
          cover: {
            coverPic: get(existingFields, 'cover.coverPic', false),
            title: get(existingFields, 'cover.title', false),
            producer: get(existingFields, 'cover.producer', false),
            director: get(existingFields, 'cover.director', false),
            writer: get(existingFields, 'cover.writer', false),
          },
          basicInfo: {
            logLine: get(existingFields, 'basicInfo.logLine', false),
            format: get(existingFields, 'basicInfo.format', false),
            tags: get(existingFields, 'basicInfo.tags', false),
            genre: get(existingFields, 'basicInfo.genre', false),
            setting: get(existingFields, 'basicInfo.setting', false),
            runningTime: get(existingFields, 'basicInfo.runningTime', false),
            status: get(existingFields, 'basicInfo.status', false),
            projectStatus: get(
              existingFields,
              'basicInfo.projectStatus',
              false
            ),
          },
          projectDisc: {
            synopsis: get(existingFields, 'projectDisc.synopsis', false),
            creativeVision: get(
              existingFields,
              'projectDisc.creativeVision',
              false
            ),
            treatment: get(existingFields, 'projectDisc.treatment', false),
            script: get(existingFields, 'projectDisc.script', false),
          },
          creativeTeam: get(existingFields, 'creativeTeam', false),
          castMembers: get(existingFields, 'castMembers', false),
          artWork: get(existingFields, 'artWork', false),
          videos: get(existingFields, 'videos', false),
          financePlan: get(existingFields, 'financePlan', false),
          budget: get(existingFields, 'budget', false),
          otherDocs: get(existingFields, 'otherDocs', false),
          salesEstimateFile: get(existingFields, 'salesEstimateFile', false),
        };
      });
    }
  }, [callout]);

  const handleFieldChange = (section, field, value) => {
    if (typeof section === 'string' && typeof field === 'boolean') {
      // Handle top-level fields like creativeTeam, castMembers, etc.
      setRequiredFields((prev) => ({
        ...prev,
        [section]: field,
      }));
    } else {
      // Handle nested fields
      setRequiredFields((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value,
        },
      }));
    }
  };

  const navigate = useNavigate();

  const isAllSectionSelected = (section) => {
    const sectionFields = requiredFields[section];
    if (typeof sectionFields === 'object') {
      return Object.values(sectionFields).every((value) => value === true);
    }
    return false;
  };

  const handleSectionToggle = (section, isCurrentlySelected) => {
    const newValue = !isCurrentlySelected;
    setRequiredFields((prev) => {
      const sectionFields = prev[section];
      const updatedSection = {};

      Object.keys(sectionFields).forEach((field) => {
        updatedSection[field] = newValue;
      });

      return {
        ...prev,
        [section]: updatedSection,
      };
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await update('v1/callout', {
        id: recordId,
        data: { requiredFields },
      });

      notify('Required fields updated successfully', { type: 'success' });
      navigate(`/callouts/${recordId}`, { replace: false });
    } catch (error) {
      notify('Error updating required fields', { type: 'error' });
      console.error('Error saving required fields:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    redirect(`/callouts/${recordId}`);
  };

  const renderSection = (title, sectionKey, fields) => (
    <Box
      sx={{
        borderRadius: 1,
        p: 2,
        backgroundColor: '#fff',
        height: '100%',
      }}
    >
      <FormControlLabel
        control={
          <Checkbox
            checked={isAllSectionSelected(sectionKey)}
            onChange={() =>
              handleSectionToggle(sectionKey, isAllSectionSelected(sectionKey))
            }
            sx={{
              display: 'block',
              color: '#000',
              '&.Mui-checked': { color: '#000' },
              '& .MuiSvgIcon-root': { fontSize: 20 },
            }}
          />
        }
        label={
          <Typography
            variant="body2"
            sx={{
              fontWeight: 'bold',
              color: '#000',
              fontSize: '0.875rem',
            }}
          >
            {title}
          </Typography>
        }
        sx={{
          mb: 1,
          ml: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
        }}
      />

      <Divider sx={{ mb: 1.5, borderColor: '#e0e0e0' }} />

      <FormGroup>
        {fields
          ? Object.entries(fields).map(([fieldKey, fieldValue]) => (
              <FormControlLabel
                key={fieldKey}
                control={
                  <Checkbox
                    checked={fieldValue}
                    onChange={(e) =>
                      handleFieldChange(sectionKey, fieldKey, e.target.checked)
                    }
                    sx={{
                      color: '#000',
                      '&.Mui-checked': { color: '#000' },
                      '& .MuiSvgIcon-root': { fontSize: 18 },
                    }}
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#555',
                      fontSize: '0.875rem',
                    }}
                  >
                    {fieldKey
                      .replace(/([A-Z])/g, ' $1')
                      .replace(/^./, (str) => str.toUpperCase())}
                  </Typography>
                }
                sx={{
                  mb: 1,
                  ml: 0,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              />
            ))
          : null}
      </FormGroup>
    </Box>
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <div className="pt-3">
        <BasicBreadcrumbs
          links={[
            { label: 'Call outs', url: '/callouts' },
            {
              label: get(callout, 'name'),
              url: `/callouts/${get(callout, 'id')}/show`,
            },
            { label: 'Edit' },
          ]}
        />
        <Title title="Edit Required Fields" />
      </div>

      <Box sx={{ width: '100%', p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#000' }}>
            Required Fields
          </Typography>
        </Box>

        {/* Main Container with Border */}
        <Box
          sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            p: 3,
            backgroundColor: '#fff',
          }}
        >
          {/* Top 3 Sections in Grid */}
          <Grid
            container
            spacing={2}
            sx={{
              mb: 3,
              '& .MuiGrid-item': {
                paddingLeft: 0,
                paddingTop: 0,
              },
              '& .css-1mi8ox7': {
                padding: 0,
              },
              '.css-2gli1q-MuiFormControlLabel-root': {
                marginBottom: 0,
              },
              '.css-oke2o7-MuiDivider-root': {
                marginBottom: 0,
              },
            }}
          >
            <Grid item xs={12} md={4}>
              {renderSection(
                'Cover (All Fields)',
                'cover',
                requiredFields.cover
              )}
            </Grid>

            <Grid item xs={12} md={4}>
              {renderSection(
                'Basic Information (All Fields)',
                'basicInfo',
                requiredFields.basicInfo
              )}
            </Grid>

            <Grid item xs={12} md={4}>
              {renderSection(
                'Description (All Fields)',
                'projectDisc',
                requiredFields.projectDisc
              )}
            </Grid>
          </Grid>

          {/* Other Fields Section */}
          <Box sx={{ mt: 3 }}>
            <Grid
              container
              spacing={2}
              sx={{
                mt: 4,
                mb: 3,
                '& .MuiGrid-item': {
                  paddingLeft: 0,
                  paddingTop: 0,
                },
              }}
            >
              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.creativeTeam}
                      onChange={(e) =>
                        handleFieldChange('creativeTeam', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Creative Team
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.financePlan}
                      onChange={(e) =>
                        handleFieldChange('financePlan', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Finance Plan
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.artWork}
                      onChange={(e) =>
                        handleFieldChange('artWork', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Mood Board
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.videos}
                      onChange={(e) =>
                        handleFieldChange('videos', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Project Videos
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.castMembers}
                      onChange={(e) =>
                        handleFieldChange('castMembers', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Cast Members
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.budget}
                      onChange={(e) =>
                        handleFieldChange('budget', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Budget
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.otherDocs}
                      onChange={(e) =>
                        handleFieldChange('otherDocs', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Other Documents
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={requiredFields.salesEstimateFile}
                      onChange={(e) =>
                        handleFieldChange('salesEstimateFile', e.target.checked)
                      }
                      sx={{
                        color: '#000',
                        '&.Mui-checked': { color: '#000' },
                        '& .MuiSvgIcon-root': { fontSize: 18 },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.875rem', color: '#555' }}
                    >
                      Sales Estimate
                    </Typography>
                  }
                  sx={{
                    mb: 1,
                    ml: 0,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box
          sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4 }}
        >
          <Button
            variant="outlined"
            onClick={handleBack}
            disabled={isSaving}
            sx={{
              backgroundColor: '#05012d',
              '&:hover': { backgroundColor: '#040124' },
              '&:disabled': { backgroundColor: '#ccc' },
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={isSaving}
            sx={{
              backgroundColor: '#05012d',
              '&:hover': { backgroundColor: '#040124' },
              '&:disabled': { backgroundColor: '#ccc' },
            }}
          >
            {isSaving ? 'Saving...' : 'Save Required Fields'}
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default RequiredFieldsEdit;
