/* eslint-disable no-useless-escape */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import HoverToolTip from 'sharedComponents/ToolTip/hoverToolTip';
import Style from 'pages/project/share/style/share.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import mixpanelAnalytics from 'lib/mixpanel';

const SnapsView = (props) => {
  const {
    snapsList,
    getProjectSnapshots,
    projectPreviewData,
    setSelectedSnapId,
    selectedSnapId,
    setSelectedSnapBody,
    submissions,
    callout,
  } = props;

  useEffect(() => {
    const fetchHistory = async () => {
      if (get(projectPreviewData, '_id')) {
        await getProjectSnapshots({
          projectId: projectPreviewData._id,
        });
      }
    };
    fetchHistory();
  }, [getProjectSnapshots, projectPreviewData, submissions]);

  const handleSnapSelect = (item) => {
    setSelectedSnapId(item._id);
    setSelectedSnapBody(item);
  };

  // Helper function to check if field value is empty
  const isEmpty = (value) => {
    return (
      value === undefined ||
      value === null ||
      (typeof value === 'string' && value.trim() === '') ||
      (Array.isArray(value) && value.length === 0)
    );
  };

  // Helper function to check missing required fields - optimized single loop
  const hasMissingFields = (parsedBody, requiredFields) => {
    if (!requiredFields || !parsedBody) return false;

    // Flatten and check all required fields in single loop
    for (const [sectionKey, sectionFields] of Object.entries(requiredFields)) {
      if (!sectionFields || typeof sectionFields !== 'object') continue;

      for (const [fieldKey, isRequired] of Object.entries(sectionFields)) {
        if (isRequired && isEmpty(parsedBody[sectionKey]?.[fieldKey])) {
          return true;
        }
      }
    }
    return false;
  };

  // Create submission lookup set for O(1) performance
  const submittedSnapIds = useMemo(() => {
    return new Set(
      submissions
        .filter((submission) => submission.snapshot?.id)
        .map((submission) => submission.snapshot.id),
    );
  }, [submissions]);

  // Memoized snap list processing
  const { snapList, showMissingFieldsError } = useMemo(() => {
    if (!snapsList) return { snapList: [], showMissingFieldsError: false };

    let hasAnyDisabledSnap = false;

    const processedSnaps = snapsList.map((item) => {
      // Parse date and time once
      const [date, timeWithMs] = item.createdAt.split('T');
      const time = timeWithMs.split('.')[0];

      // Parse body once
      let parsedBody = null;
      try {
        parsedBody = JSON.parse(item.body);
      } catch {
        parsedBody = null;
      }

      // Check if snapshot is missing required fields or already submitted
      const missingRequiredFields = hasMissingFields(
        parsedBody,
        callout?.requiredFields,
      );
      const alreadySubmitted = submittedSnapIds.has(item._id);
      const snapDisabled = alreadySubmitted || missingRequiredFields;

      if (snapDisabled) hasAnyDisabledSnap = true;

      return (
        <div
          className={`${Style.listContainer} col-12 d-flex flex-row mb-2 ${
            selectedSnapId === item._id ? Style.selectedList : ''
          } ${snapDisabled ? Style.disabledList : ''}`}
          key={item._id}
          onClick={() => !snapDisabled && handleSnapSelect(item)}
        >
          <div className={`d-flex w-100 p-0 ${snapDisabled ? 'ml-24' : ''}`}>
            <div className={`mr-24 align-self-center ${Style.radioButton}`}>
              {!snapDisabled && (
                <input
                  type="radio"
                  checked={selectedSnapId === item._id}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleSnapSelect(item);
                    mixpanelAnalytics('select_snapshot', {
                      pageName: 'callouts_projects_list',
                      snapshotName: get(item, 'notes', 'unknown title'),
                    });
                  }}
                />
              )}
            </div>
            <div className="col-9 col-md-5 col-lg-5 p-0 align-self-center">
              <p className="m-0">{item.notes || 'Unknown title'}</p>
              <p className="m-0 d-block d-sm-none mt-2">
                {date} {time}
              </p>
            </div>
            <div className="col-5 align-self-center d-none d-sm-block">
              <p className="m-0">
                {date} {time}
              </p>
            </div>
            <div className="d-flex ml-auto justify-content-around mb-2">
              <div
                className="text-center align-self-center"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(`/project/snap/${item.hash}`, '_blank');
                }}
              >
                <HoverToolTip
                  clickIcon={
                    <InlineSvg
                      height={24}
                      width={24}
                      src="/assets/svg/publishView.svg"
                    />
                  }
                  position="top"
                  contentClass="p2 text-left"
                  content="View Snapshot"
                />
              </div>
            </div>
          </div>
        </div>
      );
    });

    return {
      snapList: processedSnaps,
      showMissingFieldsError: hasAnyDisabledSnap,
    };
  }, [snapsList, selectedSnapId, submittedSnapIds, callout?.requiredFields]);

  return (
    <div className="col-12 mt-3 mb-mt-4 mb-lg-4 p-0">
      <div className="row m-0 mb-35">
        {snapList}
        {showMissingFieldsError && (
          <div className={`mt-2 ${Style.submitError}`}>
            Your snapshots don't have all the required fields, please update
            your project and create a new snapshot.
          </div>
        )}
      </div>
    </div>
  );
};

SnapsView.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  handleChangePublishSnap: PropTypes.func.isRequired,
  snapsList: PropTypes.array.isRequired,
  getHistory: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
};

export default SnapsView;
