.modalTitle {
  text-align: center;
  font-size: 20px;
  line-height: 24px;
  color: #05012d;
  margin-bottom: 0px !important;
}

.inviteBtnText {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
  background-color: #05012d;
  border: none;
  border-radius: 0; // 🟩 Flat rectangle fix
  width: 100%;
  padding: 8px 28px;
  height: 48px;

  &:hover,
  &:focus {
    background-color: #05012d;
    color: #ffffff;
    box-shadow: none;
  }
}

.inviteFormRow {
  margin-bottom: 28px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-end;
}

.invite-button-wrapper {
  flex-shrink: 0;
  width: 160px;

  @media (max-width: 576px) {
    width: 100%;
  }
}
