import React from 'react';
import { Field, reduxForm } from 'redux-form';
import { FormGroup } from 'react-bootstrap';
import Ren<PERSON><PERSON>ield from 'sharedComponents/renderfield';
import style from '../styles/shareModal.module.scss';

// Validation function
const required = (value) => (value && value.trim() ? undefined : 'Required');

const Share = ({ handleSubmit, submitSnapshotForm, invalid, pristine }) => {
  const isDisabled = invalid || pristine;

  return (
    <>
      <form onSubmit={handleSubmit(submitSnapshotForm)} className="formStyle">
        <div
          className={`d-flex flex-wrap gap-3 align-items-end ${style.inviteFormRow}`}
        >
          <div className="flex-grow-1">
            <FormGroup name="formGroup" className="mb-0 w-100">
              <Field
                name="snapshotTitle"
                component={RenderField}
                validate={[required]}
                placeholder={`Add a short title to help you identify versions of your Snapshot.
                  
Titles might include the company or the person you are sharing it with, e.g. Catalonia Films or <PERSON>. A keyword, version number, email address or a new team member. You might want to add notes about unfinished sections, e.g. Needs Finance.`}
                size="lg"
                type="synopsistextarea"
                modalHeader="modalTitle"
              />
            </FormGroup>
          </div>
        </div>

        <div
          className="d-flex justify-content-end mt-3"
          style={{ width: '100%' }}
        >
          <button
            type="submit"
            id="submit_btn"
            className={`${style.inviteBtnText}`}
            style={{
              width: '25%',
              opacity: isDisabled ? 0.5 : 1,
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              transition: 'opacity 0.3s ease',
            }}
            disabled={isDisabled}
          >
            ADD
          </button>
        </div>
      </form>
    </>
  );
};

export default reduxForm({
  form: 'snapshotTitleForm',
})(Share);
