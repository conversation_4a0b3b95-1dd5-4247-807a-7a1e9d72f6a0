import React, { useRef, useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON><PERSON>,
  CircleStencil,
  RectangleStencil,
  ImageRestriction,
} from 'react-advanced-cropper';
import 'react-advanced-cropper/dist/style.css';
import styles from './ImageCropper.module.scss';
import Button from 'sharedComponents/Button/button';

const ImageCropper = ({
  imageSrc,
  onCropComplete,
  onCancel,
  isVisible,
  cropShape = 'round',
  isAspectRatio = '',
}) => {
  const cropperRef = useRef(null);
  const containerRef = useRef(null);
  const [zoomLevel, setZoomLevel] = useState(5);

  const getCroppedImageBlob = (canvas) =>
    new Promise((resolve) => canvas.toBlob(resolve, 'image/jpeg', 0.9));

  const handleCropComplete = async () => {
    if (cropperRef.current) {
      const canvas = cropperRef.current.getCanvas();
      if (canvas) {
        const blob = await getCroppedImageBlob(canvas);
        onCropComplete(blob);
      }
    }
  };

  const handleZoom = useCallback(
    (direction, e) => {
      e?.preventDefault();
      e?.stopPropagation();
      if (!cropperRef.current) return;

      const factor = direction > 0 ? 1.2 : 0.8;
      const newLevel =
        direction > 0
          ? Math.min(10, zoomLevel + 1)
          : Math.max(1, zoomLevel - 1);

      if (newLevel !== zoomLevel) {
        cropperRef.current.zoomImage(factor, { transitions: false });
        setZoomLevel(newLevel);
      }
    },
    [zoomLevel],
  );

  const handleSliderChange = useCallback(
    (e) => {
      const newLevel = parseInt(e.target.value);
      const diff = newLevel - zoomLevel;

      if (diff !== 0 && cropperRef.current) {
        const factor = diff > 0 ? 1.2 : 0.8;
        const steps = Math.abs(diff);

        for (let i = 0; i < steps; i++) {
          setTimeout(() => {
            if (cropperRef.current) {
              cropperRef.current.zoomImage(factor, { transitions: false });
            }
          }, i * 50);
        }
        setZoomLevel(newLevel);
      }
    },
    [zoomLevel],
  );

  const preventZoomEvents = useCallback((e) => {
    if (e.type === 'wheel' || (e.touches && e.touches.length > 1)) {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      return false;
    }
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const events = ['wheel', 'touchstart', 'touchmove'];
    const options = { capture: true, passive: false };

    events.forEach((event) =>
      container.addEventListener(event, preventZoomEvents, options),
    );
    return () =>
      events.forEach((event) =>
        container.removeEventListener(event, preventZoomEvents, options),
      );
  }, [isVisible, preventZoomEvents]);

  if (!isVisible) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        {/* Header */}
        <div className={styles.header}>
          <h2 className={styles.title}>CROP IMAGE</h2>
          <button
            className={styles.closeBtn}
            onClick={onCancel}
            type="button"
            aria-label="Close"
          >
            ✕
          </button>
        </div>
        {/* Cropper Area */}
        <div
          ref={containerRef}
          className={styles.cropperArea}
          onWheel={preventZoomEvents}
          onTouchStart={preventZoomEvents}
          onTouchMove={preventZoomEvents}
        >
          <Cropper
            ref={cropperRef}
            src={imageSrc}
            stencilComponent={
              cropShape === 'round' ? CircleStencil : RectangleStencil
            }
            stencilProps={{
              movable: true,
              resizable: cropShape !== 'round',
              aspectRatio: cropShape === 'round' ? 1 : isAspectRatio,
            }}
            imageRestriction={ImageRestriction.stencil}
            wheelResize={false}
            touchResize={false}
            onWheel={preventZoomEvents}
          />
        </div>
        {/* Controls */}
        <div className={styles.controls}>
          <button
            type="button"
            className={styles.zoomBtn}
            onClick={(e) => handleZoom(-1, e)}
            disabled={zoomLevel <= 1}
            aria-label="Zoom out"
          >
            −
          </button>
          <input
            type="range"
            min={1}
            max={10}
            step={1}
            value={zoomLevel}
            onChange={handleSliderChange}
            className={styles.slider}
            aria-label="Zoom level"
          />
          <button
            type="button"
            className={styles.zoomBtn}
            onClick={(e) => handleZoom(1, e)}
            disabled={zoomLevel >= 10}
            aria-label="Zoom in"
          >
            +
          </button>
          <Button
            btntype="button"
            customClass="waitListBtn"
            buttonValue="APPLY"
            clickHandler={handleCropComplete}
          />
        </div>
      </div>
    </div>
  );
};

ImageCropper.propTypes = {
  imageSrc: PropTypes.string.isRequired,
  onCropComplete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  isVisible: PropTypes.bool.isRequired,
  cropShape: PropTypes.oneOf(['rect', 'round']),
};

export default ImageCropper;
