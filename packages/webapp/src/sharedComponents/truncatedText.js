import React, { useState } from 'react';

const TruncatedHTML = ({ text, maxLength = 321, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return null;

  // Remove HTML tags for counting characters
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = text;
  const plainText = tempDiv.textContent || tempDiv.innerText || '';

  const shouldShowMore = plainText.length > maxLength;
  const displayHTML = isExpanded
    ? text
    : plainText.slice(0, maxLength) + (plainText.length > maxLength ? '' : '');

  const toggleExpanded = () => setIsExpanded(!isExpanded);

  return (
    <p className={className}>
      <span
        className={className}
        dangerouslySetInnerHTML={{ __html: displayHTML }}
      />
      {shouldShowMore && (
        <span
          style={{ cursor: 'pointer', color: '#1743d7' }}
          onClick={toggleExpanded}
          className={className}
        >
          {isExpanded ? ' less' : ' more...'}
        </span>
      )}
    </p>
  );
};

export default TruncatedHTML;
