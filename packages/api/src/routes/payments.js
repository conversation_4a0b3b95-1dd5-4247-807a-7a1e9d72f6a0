/* eslint-disable no-multi-str */
const PaymentsController = require('../controllers/PaymentsController');
const WebhookController = require('../controllers/WebhookController');
const { responseCodes } = require('../utils/respSchemaHandler');
const { payments } = require('../validate/payments');

module.exports = {
  plugin: {
    async register(server, options) {
      server.route([
        {
          method: 'get',
          path: '/stripe/user/subscriptions',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Payments'],
            validate: {},
            pre: [],
            handler: PaymentsController.getUserStripSubscriptions,
            description: `Payments`,
          },
        },
      ]);

      server.route([
        {
          method: 'POST',
          path: '/stripe/subscriptions',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Payments'],
            validate: {
              payload: payments.stripeSubscriptionsSchema,
            },
            pre: [],
            handler: PaymentsController.createStripSubscriptions,
            description: `Payments`,
          },
        },
      ]);
      server.route([
        {
          method: 'GET',
          path: '/customer',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Payments'],
            validate: {},
            pre: [],
            handler: PaymentsController.createCustomerInStripe,
            description: `To check customer in stripe and create`,
          },
        },
      ]);
      server.route([
        {
          method: 'GET',
          path: '/subscriptions/{subscriptionId}/canceled',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Payments'],
            validate: payments.canceledSubscription,
            pre: [],
            handler: PaymentsController.canceledSubsctiption,
            description: `canceled subscription`,
          },
        },
      ]);
      server.route([
        {
          method: 'POST',
          path: '/subscriptions/upgrade',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Payments'],
            validate: payments.upgradeSubscription,
            pre: [],
            handler: PaymentsController.upgradeSubscriptionInStripe,
            description: `upgrade subscription`,
          },
        },
      ]);
      server.route([
        {
          method: 'POST',
          path: '/stripe/webhook',
          options: {
            plugins: {
              'hapi-swagger': {
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            auth: false,
            tags: ['no-swagger'],
            validate: {},
            pre: [],
            payload: {
              parse: false,
            },
            handler: WebhookController.stripeWebhook,
            description: `Payments`,
          },
        },
      ]);
    },
    version: process.env.API_VERSION,
    name: 'payments',
  },
};
